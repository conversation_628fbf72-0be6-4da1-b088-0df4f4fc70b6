#!/usr/bin/env python3
"""
Simple script to test OpenAI API with an empty prompt string.
This script follows the patterns used in the OpenEngage codebase.
"""

import os
from dotenv import load_dotenv
from openai import OpenAI

def main():
    """Main function to test OpenAI API with empty prompt."""
    
    # Load environment variables from .env file
    load_dotenv()
    
    # Get OpenAI API key from environment
    api_key = os.getenv("OPENAI_API_KEY")
    
    if not api_key:
        print("❌ Error: OPENAI_API_KEY not found in environment variables.")
        print("Please set your OpenAI API key in a .env file or environment variable.")
        print("Example .env file content:")
        print("OPENAI_API_KEY=your_api_key_here")
        return
    
    try:
        # Initialize OpenAI client
        print("🔧 Initializing OpenAI client...")
        client = OpenAI(api_key=api_key)
        print("✅ OpenAI client initialized successfully!")
        
        # Test with empty prompt string
        print("\n🚀 Testing OpenAI API with empty prompt string...")
        
        response = client.chat.completions.create(
            model="gpt-4o-mini",  # Using the preferred model from the codebase
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant. If you receive an empty or minimal prompt, respond with a brief, friendly message."
                },
                {
                    "role": "user", 
                    "content": ""  # Empty prompt string as requested
                }
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        # Extract and display the response
        ai_response = response.choices[0].message.content
        print(f"✅ API call successful!")
        print(f"📝 Response from OpenAI:")
        print(f"   {ai_response}")
        
        # Display some metadata
        print(f"\n📊 Response metadata:")
        print(f"   Model used: {response.model}")
        print(f"   Tokens used: {response.usage.total_tokens}")
        print(f"   Prompt tokens: {response.usage.prompt_tokens}")
        print(f"   Completion tokens: {response.usage.completion_tokens}")
        
    except Exception as e:
        print(f"❌ Error occurred while calling OpenAI API:")
        print(f"   {str(e)}")
        print("\n💡 Common issues:")
        print("   - Invalid API key")
        print("   - Insufficient API credits")
        print("   - Network connectivity issues")
        print("   - Rate limiting")

if __name__ == "__main__":
    print("🤖 OpenAI API Test Script")
    print("=" * 40)
    main()
    print("=" * 40)
    print("✨ Script completed!")
